import React from "react";
import { useNavigate } from "react-router-dom";

const EmergencySupport: React.FC = () => {
  const navigate = useNavigate();

  const handleCallClick = () => {
    navigate("/video-call");
  };

  return (
    <div className="px-4 py-3">
      <div
        className="relative rounded-xl overflow-hidden min-h-[246px] flex items-end"
        style={{
          background: `linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.00) 100%), 
                       linear-gradient(135deg, #6B7B7F 0%, #4A5A5E  100%)`,
        }}
      >
        <div className="p-4 w-full flex justify-between items-end">
          <div className="flex-1 max-w-[440px]">
            <h2 className="text-white text-2xl font-bold font-['Lexend'] leading-[30px] mb-1">
              Emergency
              <br />
              Support Line
            </h2>
            <p className="text-white text-base font-['Lexend'] leading-6">
              7/24 Expert Support
            </p>
          </div>
          <button
            className="bg-[#B512EB] text-white px-4 py-2 rounded-xl min-w-[84px] h-10 flex items-center justify-center"
            onClick={handleCallClick}
          >
            <span className="text-sm font-bold font-['Lexend']">Call</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmergencySupport;
