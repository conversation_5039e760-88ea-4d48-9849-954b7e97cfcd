import React from "react";

interface IconProps {
  className?: string;
  size?: number;
}

export const SettingsIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 5.5C7.51472 5.5 5.5 7.51472 5.5 10C5.5 12.4853 7.51472 14.5 10 14.5C12.4853 14.5 14.5 12.4853 14.5 10C14.4974 7.51579 12.4842 5.50258 10 5.5ZM10 13C8.34315 13 7 11.6569 7 10C7 8.34315 8.34315 7 10 7C11.6569 7 13 8.34315 13 10C13 11.6569 11.6569 13 10 13ZM18.25 10.2025C18.2537 10.0675 18.2537 9.9325 18.25 9.7975L19.6488 8.05C19.7975 7.86393 19.849 7.61827 19.7875 7.38813C19.5582 6.52619 19.2152 5.69861 18.7675 4.92719C18.6486 4.72249 18.4401 4.58592 18.205 4.55875L15.9813 4.31125C15.8888 4.21375 15.795 4.12 15.7 4.03L15.4375 1.80063C15.4101 1.56531 15.2732 1.35677 15.0681 1.23813C14.2964 0.791263 13.4689 0.448595 12.6072 0.219063C12.3769 0.157836 12.1312 0.209687 11.9453 0.35875L10.2025 1.75C10.0675 1.75 9.9325 1.75 9.7975 1.75L8.05 0.354063C7.86393 0.205326 7.61827 0.153827 7.38813 0.215312C6.52633 0.445025 5.6988 0.788016 4.92719 1.23531C4.72249 1.35417 4.58592 1.56268 4.55875 1.79781L4.31125 4.02531C4.21375 4.11844 4.12 4.21219 4.03 4.30656L1.80063 4.5625C1.56531 4.58988 1.35677 4.72682 1.23813 4.93188C0.791263 5.70359 0.448595 6.5311 0.219063 7.39281C0.157836 7.6231 0.209687 7.86878 0.35875 8.05469L1.75 9.7975C1.75 9.9325 1.75 10.0675 1.75 10.2025L0.354063 11.95C0.205326 12.1361 0.153827 12.3817 0.215312 12.6119C0.444615 13.4738 0.787627 14.3014 1.23531 15.0728C1.35417 15.2775 1.56268 15.4141 1.79781 15.4412L4.02156 15.6887C4.11469 15.7862 4.20844 15.88 4.30281 15.97L4.5625 18.1994C4.58988 18.4347 4.72682 18.6432 4.93188 18.7619C5.70359 19.2087 6.5311 19.5514 7.39281 19.7809C7.6231 19.8422 7.86878 19.7903 8.05469 19.6413L9.7975 18.25C9.9325 18.2537 10.0675 18.2537 10.2025 18.25L11.95 19.6488C12.1361 19.7975 12.3817 19.849 12.6119 19.7875C13.4738 19.5582 14.3014 19.2152 15.0728 18.7675C15.2775 18.6486 15.4141 18.4401 15.4412 18.205L15.6887 15.9813C15.7862 15.8888 15.88 15.795 15.97 15.7L18.1994 15.4375C18.4347 15.4101 18.6432 15.2732 18.7619 15.0681C19.2087 14.2964 19.5514 13.4689 19.7809 12.6072C19.8422 12.3769 19.7903 12.1312 19.6413 11.9453L18.25 10.2025Z"
      fill="currentColor"
    />
  </svg>
);

export const SearchIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.5306 18.4694L14.8366 13.7762C17.6629 10.383 17.3204 5.36693 14.0591 2.38935C10.7978 -0.588237 5.77134 -0.474001 2.64867 2.64867C-0.474001 5.77134 -0.588237 10.7978 2.38935 14.0591C5.36693 17.3204 10.383 17.6629 13.7762 14.8366L18.4694 19.5306C18.7624 19.8237 19.2376 19.8237 19.5306 19.5306C19.8237 19.2376 19.8237 18.7624 19.5306 18.4694ZM1.75 8.5C1.75 4.77208 4.77208 1.75 8.5 1.75C12.2279 1.75 15.25 4.77208 15.25 8.5C15.25 12.2279 12.2279 15.25 8.5 15.25C4.77379 15.2459 1.75413 12.2262 1.75 8.5Z"
      fill="currentColor"
    />
  </svg>
);

export const OnlineConsultationIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 18"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.4163 6.87562L8.91625 3.87563C8.68605 3.72204 8.38998 3.70769 8.14601 3.83831C7.90204 3.96892 7.74982 4.22327 7.75 4.5V10.5C7.74982 10.7767 7.90204 11.0311 8.14601 11.1617C8.38998 11.2923 8.68605 11.278 8.91625 11.1244L13.4163 8.12438C13.6252 7.98533 13.7507 7.75098 13.7507 7.5C13.7507 7.24902 13.6252 7.01467 13.4163 6.87562ZM9.25 9.09844V5.90625L11.6481 7.5L9.25 9.09844ZM18.25 0.75H1.75C0.921573 0.75 0.25 1.42157 0.25 2.25V12.75C0.25 13.5784 0.921573 14.25 1.75 14.25H18.25C19.0784 14.25 19.75 13.5784 19.75 12.75V2.25C19.75 1.42157 19.0784 0.75 18.25 0.75ZM18.25 12.75H1.75V2.25H18.25V12.75ZM19.75 16.5C19.75 16.9142 19.4142 17.25 19 17.25H1C0.585786 17.25 0.25 16.9142 0.25 16.5C0.25 16.0858 0.585786 15.75 1 15.75H19C19.4142 15.75 19.75 16.0858 19.75 16.5Z"
      fill="currentColor"
    />
  </svg>
);

export const CycleTrackingIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 20"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.5 2H14.25V1.25C14.25 0.835786 13.9142 0.5 13.5 0.5C13.0858 0.5 12.75 0.835786 12.75 1.25V2H5.25V1.25C5.25 0.835786 4.91421 0.5 4.5 0.5C4.08579 0.5 3.75 0.835786 3.75 1.25V2H1.5C0.671573 2 0 2.67157 0 3.5V18.5C0 19.3284 0.671573 20 1.5 20H16.5C17.3284 20 18 19.3284 18 18.5V3.5C18 2.67157 17.3284 2 16.5 2ZM3.75 3.5V4.25C3.75 4.66421 4.08579 5 4.5 5C4.91421 5 5.25 4.66421 5.25 4.25V3.5H12.75V4.25C12.75 4.66421 13.0858 5 13.5 5C13.9142 5 14.25 4.66421 14.25 4.25V3.5H16.5V6.5H1.5V3.5H3.75ZM16.5 18.5H1.5V8H16.5V18.5Z"
      fill="currentColor"
    />
  </svg>
);

export const SexualTherapyIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 22 18"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.6875 0C13.7516 0 12.0566 0.8325 11 2.23969C9.94344 0.8325 8.24844 0 6.3125 0C3.10384 0.00361657 0.503617 2.60384 0.5 5.8125C0.5 12.375 10.2303 17.6869 10.6447 17.9062C10.8665 18.0256 11.1335 18.0256 11.3553 17.9062C11.7697 17.6869 21.5 12.375 21.5 5.8125C21.4964 2.60384 18.8962 0.00361657 15.6875 0ZM11 16.3875C9.28813 15.39 2 10.8459 2 5.8125C2.0031 3.43206 3.93206 1.5031 6.3125 1.5C8.13594 1.5 9.66687 2.47125 10.3062 4.03125C10.4218 4.31259 10.6959 4.49627 11 4.49627C11.3041 4.49627 11.5782 4.31259 11.6938 4.03125C12.3331 2.46844 13.8641 1.5 15.6875 1.5C18.0679 1.5031 19.9969 3.43206 20 5.8125C20 10.8384 12.71 15.3891 11 16.3875Z"
      fill="currentColor"
    />
  </svg>
);

export const EggReserveIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 19"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.5 0.75H2.5C1.25736 0.75 0.25 1.75736 0.25 3V13.5C0.25 14.7426 1.25736 15.75 2.5 15.75H17.5C18.7426 15.75 19.75 14.7426 19.75 13.5V3C19.75 1.75736 18.7426 0.75 17.5 0.75ZM18.25 13.5C18.25 13.9142 17.9142 14.25 17.5 14.25H2.5C2.08579 14.25 1.75 13.9142 1.75 13.5V3C1.75 2.58579 2.08579 2.25 2.5 2.25H17.5C17.9142 2.25 18.25 2.58579 18.25 3V13.5ZM13.75 18C13.75 18.4142 13.4142 18.75 13 18.75H7C6.58579 18.75 6.25 18.4142 6.25 18C6.25 17.5858 6.58579 17.25 7 17.25H13C13.4142 17.25 13.75 17.5858 13.75 18Z"
      fill="currentColor"
    />
  </svg>
);

export const MenstrualTrackingIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 22"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.4994 4.58375C12.7941 2.02719 10.7381 0.5 9 0.5C7.26188 0.5 5.20594 2.02719 3.50063 4.58375C1.75313 7.20687 0.75 10.3653 0.75 13.25C0.75 17.8063 4.44365 21.5 9 21.5C13.5563 21.5 17.25 17.8063 17.25 13.25C17.25 10.3653 16.2469 7.20687 14.4994 4.58375ZM9 20C5.27379 19.9959 2.25413 16.9762 2.25 13.25C2.25 10.6541 3.16125 7.79844 4.74937 5.41625C6.11156 3.3725 7.81969 2 9 2C10.1803 2 11.8884 3.3725 13.2506 5.41625C14.8388 7.79844 15.75 10.6541 15.75 13.25C15.7459 16.9762 12.7262 19.9959 9 20Z"
      fill="currentColor"
    />
  </svg>
);

export const AcademyIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 22"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.6025 6.33813L12.3525 0.338125C12.1321 0.220791 11.8679 0.220791 11.6475 0.338125L0.3975 6.33813C0.152987 6.46843 0.000234297 6.72293 0.000234297 7C0.000234297 7.27707 0.152987 7.53157 0.3975 7.66187L3 9.05031V13.5897C2.99922 13.9581 3.1348 14.3137 3.38063 14.5881C4.60875 15.9559 7.36031 18.25 12 18.25C13.5384 18.2627 15.0653 17.9841 16.5 17.4287V20.5C16.5 20.9142 16.8358 21.25 17.25 21.25C17.6642 21.25 18 20.9142 18 20.5V16.7041C18.978 16.1395 19.8618 15.4256 20.6194 14.5881C20.8652 14.3137 21.0008 13.9581 21 13.5897V9.05031L23.6025 7.66187C23.847 7.53157 23.9998 7.27707 23.9998 7C23.9998 6.72293 23.847 6.46843 23.6025 6.33813Z"
      fill="currentColor"
    />
  </svg>
);

export const HealthyLivingIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 18"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.9484 0.756562C17.9263 0.376768 17.6232 0.0737419 17.2434 0.0515625C10.1062 -0.3675 4.38938 1.78125 1.95188 5.8125C1.10697 7.19148 0.690616 8.79032 0.755625 10.4062C0.809062 11.8988 1.24406 13.4062 2.04844 14.8922L0.219375 16.7203C0.0298009 16.9099 -0.0442362 17.1862 0.0251527 17.4452C0.0945415 17.7041 0.296815 17.9064 0.555778 17.9758C0.814741 18.0452 1.09105 17.9711 1.28062 17.7816L3.10875 15.9525C4.59375 16.7559 6.10219 17.1909 7.59375 17.2444C7.69812 17.2481 7.80219 17.25 7.90594 17.25C9.41695 17.254 10.8993 16.8379 12.1875 16.0481C16.2188 13.6106 18.3684 7.89469 17.9484 0.756562Z"
      fill="currentColor"
    />
  </svg>
);

export const CancerScreeningIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 18 24"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.25 9C17.2523 5.16737 14.6148 1.83791 10.8834 0.962959C7.15197 0.0880083 3.3094 1.89802 1.60774 5.33218C-0.093909 8.76633 0.793552 12.9201 3.75 15.3591V22.5C3.74981 22.7601 3.88437 23.0017 4.10558 23.1384C4.32678 23.2752 4.60305 23.2876 4.83563 23.1712L9 21.0938L13.1653 23.1759C13.2697 23.2259 13.3842 23.2512 13.5 23.25C13.9142 23.25 14.25 22.9142 14.25 22.5V15.3591C16.15 13.7942 17.2505 11.4615 17.25 9Z"
      fill="currentColor"
    />
  </svg>
);

export const HomeIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 19 19"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.75 8.83281V17.5C18.75 18.3284 18.0784 19 17.25 19H13.5C12.6716 19 12 18.3284 12 17.5V13.75C12 13.3358 11.6642 13 11.25 13H8.25C7.83579 13 7.5 13.3358 7.5 13.75V17.5C7.5 18.3284 6.82843 19 6 19H2.25C1.42157 19 0.75 18.3284 0.75 17.5V8.83281C0.749936 8.41309 0.92573 8.01254 1.23469 7.72844L8.73469 0.652188L8.745 0.641875C9.31719 0.121501 10.1912 0.121501 10.7634 0.641875C10.7666 0.645543 10.7701 0.648989 10.7738 0.652188L18.2738 7.72844C18.5796 8.01402 18.7522 8.41437 18.75 8.83281Z"
      fill="currentColor"
    />
  </svg>
);

export const CalendarIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 19 20"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.75 2H14.5V1.25C14.5 0.835786 14.1642 0.5 13.75 0.5C13.3358 0.5 13 0.835786 13 1.25V2H5.5V1.25C5.5 0.835786 5.16421 0.5 4.75 0.5C4.33579 0.5 4 0.835786 4 1.25V2H1.75C0.921573 2 0.25 2.67157 0.25 3.5V18.5C0.25 19.3284 0.921573 20 1.75 20H16.75C17.5784 20 18.25 19.3284 18.25 18.5V3.5C18.25 2.67157 17.5784 2 16.75 2Z"
      fill="currentColor"
    />
  </svg>
);

export const MessageIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.875 10C10.875 10.6213 10.3713 11.125 9.75 11.125C9.12868 11.125 8.625 10.6213 8.625 10C8.625 9.37868 9.12868 8.875 9.75 8.875C10.3713 8.875 10.875 9.37868 10.875 10ZM5.625 8.875C5.00368 8.875 4.5 9.37868 4.5 10C4.5 10.6213 5.00368 11.125 5.625 11.125C6.24632 11.125 6.75 10.6213 6.75 10C6.75 9.37868 6.24632 8.875 5.625 8.875ZM13.875 8.875C13.2537 8.875 12.75 9.37868 12.75 10C12.75 10.6213 13.2537 11.125 13.875 11.125C14.4963 11.125 15 10.6213 15 10C15 9.37868 14.4963 8.875 13.875 8.875Z"
      fill="currentColor"
    />
  </svg>
);

export const ProfileIcon: React.FC<IconProps> = ({
  className = "",
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.8988 17.875C18.4709 15.4066 16.2706 13.6366 13.7028 12.7975C16.3135 11.2433 17.5641 8.13638 16.7582 5.2069C15.9522 2.27741 13.2883 0.247449 10.25 0.247449C7.21167 0.247449 4.54779 2.27741 3.74182 5.2069C2.93585 8.13638 4.18645 11.2433 6.79719 12.7975C4.22938 13.6356 2.02906 15.4056 0.60125 17.875C0.458704 18.1074 0.453527 18.3989 0.587731 18.6363C0.721935 18.8736 0.974375 19.0194 1.24702 19.0171C1.51967 19.0147 1.76958 18.8646 1.89969 18.625C3.66594 15.5725 6.78781 13.75 10.25 13.75C13.7122 13.75 16.8341 15.5725 18.6003 18.625Z"
      fill="currentColor"
    />
  </svg>
);
