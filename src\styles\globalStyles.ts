import { StyleSheet } from "react-native";

export const globalStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  row: {
    flexDirection: "row",
  },
  column: {
    flexDirection: "column",
  },

  // Spacing
  p4: {
    padding: 16,
  },
  p2: {
    padding: 8,
  },
  m4: {
    margin: 16,
  },
  m2: {
    margin: 8,
  },
  mt4: {
    marginTop: 16,
  },
  mb4: {
    marginBottom: 16,
  },
  mx4: {
    marginHorizontal: 16,
  },
  my4: {
    marginVertical: 16,
  },

  // Text styles
  textCenter: {
    textAlign: "center",
  },
  textLeft: {
    textAlign: "left",
  },
  textRight: {
    textAlign: "right",
  },

  // Typography
  h1: {
    fontSize: 32,
    fontWeight: "bold",
    lineHeight: 40,
  },
  h2: {
    fontSize: 24,
    fontWeight: "bold",
    lineHeight: 32,
  },
  h3: {
    fontSize: 20,
    fontWeight: "600",
    lineHeight: 28,
  },
  h4: {
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
  },
  caption: {
    fontSize: 14,
    lineHeight: 20,
  },
  small: {
    fontSize: 12,
    lineHeight: 16,
  },

  // Colors
  bgPrimary: {
    backgroundColor: "#1e293b",
  },
  bgSecondary: {
    backgroundColor: "#64748b",
  },
  bgLight: {
    backgroundColor: "#f8fafc",
  },
  bgWhite: {
    backgroundColor: "#ffffff",
  },

  textPrimary: {
    color: "#1e293b",
  },
  textSecondary: {
    color: "#64748b",
  },
  textLight: {
    color: "#94a3b8",
  },
  textWhite: {
    color: "#ffffff",
  },

  // Borders
  border: {
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  borderRadius: {
    borderRadius: 8,
  },
  borderRadiusLarge: {
    borderRadius: 12,
  },

  // Shadows
  shadow: {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  shadowLarge: {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },

  // Utilities
  flex1: {
    flex: 1,
  },
  flex2: {
    flex: 2,
  },
  flexGrow: {
    flexGrow: 1,
  },
  justifyCenter: {
    justifyContent: "center",
  },
  justifyBetween: {
    justifyContent: "space-between",
  },
  justifyAround: {
    justifyContent: "space-around",
  },
  alignCenter: {
    alignItems: "center",
  },
  alignStart: {
    alignItems: "flex-start",
  },
  alignEnd: {
    alignItems: "flex-end",
  },

  // Position
  absolute: {
    position: "absolute",
  },
  relative: {
    position: "relative",
  },

  // Overflow
  hidden: {
    overflow: "hidden",
  },
});
