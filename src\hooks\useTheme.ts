import { useState, useContext, createContext } from "react";
import { getTheme, Theme } from "../theme";

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(
  undefined,
);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

export const useThemeState = (initialDark = false) => {
  const [isDark, setIsDark] = useState(initialDark);
  const theme = getTheme(isDark);

  const toggleTheme = () => {
    setIsDark((prev) => !prev);
  };

  return {
    theme,
    isDark,
    toggleTheme,
  };
};
