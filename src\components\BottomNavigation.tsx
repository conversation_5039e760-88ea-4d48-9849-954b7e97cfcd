import React from "react";
import { HomeIcon, CalendarIcon, MessageIcon, ProfileIcon } from "./Icons";

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  active?: boolean;
}

interface BottomNavigationProps {
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  activeTab = "home",
  onTabChange = () => {},
}) => {
  const navItems: NavItem[] = [
    {
      id: "home",
      label: "Home",
      icon: <HomeIcon size={19} />,
      active: activeTab === "home",
    },
    {
      id: "appointments",
      label: "Appointments",
      icon: <CalendarIcon size={19} />,
      active: activeTab === "appointments",
    },
    {
      id: "messages",
      label: "Messages",
      icon: <MessageIcon size={20} />,
      active: activeTab === "messages",
    },
    {
      id: "profile",
      label: "Profile",
      icon: <ProfileIcon size={20} />,
      active: activeTab === "profile",
    },
  ];

  return (
    <div className="bg-white border-t border-[#F2F0F5]">
      <div className="flex">
        {navItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onTabChange(item.id)}
            className={`flex-1 flex flex-col items-center gap-1 py-2 px-2 ${
              item.active ? "text-[#171217]" : "text-[#80618A]"
            }`}
          >
            <div className="h-8 flex items-center justify-center">
              <div
                className={item.active ? "text-[#171217]" : "text-[#80618A]"}
              >
                {item.icon}
              </div>
            </div>
            <span
              className={`text-xs font-['Lexend'] ${
                item.active ? "text-[#171217] font-medium" : "text-[#80618A]"
              }`}
            >
              {item.label}
            </span>
          </button>
        ))}
      </div>
      <div className="h-5 bg-white"></div>
    </div>
  );
};

export default BottomNavigation;
