import React, { useState } from "react";
import Header from "../components/Header";
import SearchBar from "../components/SearchBar";
import EmergencySupport from "../components/EmergencySupport";
import ServiceCard from "../components/ServiceCard";
import EventCard from "../components/EventCard";
import VideoCard from "../components/VideoCard";
import BottomNavigation from "../components/BottomNavigation";
import {
  OnlineConsultationIcon,
  CycleTrackingIcon,
  SexualTherapyIcon,
  EggReserveIcon,
  MenstrualTrackingIcon,
  AcademyIcon,
  HealthyLivingIcon,
  CancerScreeningIcon,
} from "../components/Icons";

const Index = () => {
  const [activeTab, setActiveTab] = useState("home");

  const services = [
    {
      icon: <OnlineConsultationIcon className="text-[#171217]" size={24} />,
      title: "Online Consultation",
    },
    {
      icon: <CycleTrackingIcon className="text-[#171217]" size={24} />,
      title: "Cycle Tracking",
    },
    {
      icon: <SexualTherapyIcon className="text-[#171217]" size={24} />,
      title: "Sexual Therapy",
    },
    {
      icon: <EggReserveIcon className="text-[#171217]" size={24} />,
      title: "Egg Reserve",
    },
    {
      icon: <MenstrualTrackingIcon className="text-[#171217]" size={24} />,
      title: "Menstrual Tracking",
    },
    {
      icon: <AcademyIcon className="text-[#171217]" size={24} />,
      title: "Academy",
    },
    {
      icon: <HealthyLivingIcon className="text-[#171217]" size={24} />,
      title: "Healthy Living",
    },
    {
      icon: <CancerScreeningIcon className="text-[#171217]" size={24} />,
      title: "Cancer Screening",
    },
  ];

  const upcomingEvents = [
    {
      title: "Webinar: Understanding PCOS",
      doctor: "Dr. Amelia Carter",
    },
    {
      title: "Webinar: Fertility Options",
      doctor: "Dr. Sophia Bennett",
    },
  ];

  const mostWatchedVideos = [
    {
      title: "Understanding Endometriosis",
      doctor: "Dr. Olivia Harper",
    },
    {
      title: "Managing Menopause",
      doctor: "Dr. Isabella Hayes",
    },
  ];

  return (
    <div className="min-h-screen bg-white flex flex-col max-w-[390px] mx-auto">
      {/* Header */}
      <Header />

      {/* Search Bar */}
      <SearchBar />

      {/* Emergency Support */}
      <EmergencySupport />

      {/* Services Grid */}
      <div className="px-4 py-4">
        <div className="grid grid-cols-2 gap-3">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              icon={service.icon}
              title={service.title}
              onClick={() => console.log(`Clicked ${service.title}`)}
            />
          ))}
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="px-4 py-5">
        <h2 className="text-[#171217] text-[22px] font-bold font-['Lexend'] leading-7 mb-3">
          Upcoming Events
        </h2>
        <div className="flex gap-3 overflow-x-auto">
          {upcomingEvents.map((event, index) => (
            <EventCard
              key={index}
              title={event.title}
              doctor={event.doctor}
              onClick={() => console.log(`Clicked ${event.title}`)}
            />
          ))}
        </div>
      </div>

      {/* Most Watched Videos */}
      <div className="px-4 py-5">
        <h2 className="text-[#171217] text-[22px] font-bold font-['Lexend'] leading-7 mb-3">
          Most Watched Videos
        </h2>
        <div className="flex gap-3 overflow-x-auto">
          {mostWatchedVideos.map((video, index) => (
            <VideoCard
              key={index}
              title={video.title}
              doctor={video.doctor}
              onClick={() => console.log(`Clicked ${video.title}`)}
            />
          ))}
        </div>
      </div>

      {/* Spacer for bottom navigation */}
      <div className="flex-1 min-h-[20px]"></div>

      {/* Bottom Navigation */}
      <BottomNavigation activeTab={activeTab} onTabChange={setActiveTab} />
    </div>
  );
};

export default Index;
