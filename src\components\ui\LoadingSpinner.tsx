import React, { useEffect, useRef } from "react";
import { Animated, Easing, StyleSheet, View } from "react-native";
import Svg, { Circle } from "react-native-svg";

interface LoadingSpinnerProps {
  size?: "small" | "medium" | "large";
  color?: string;
}

const sizeMap = {
  small: 20,
  medium: 32,
  large: 40,
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "medium",
  color = "#64748b",
}) => {
  const spinValue = useRef(new Animated.Value(0)).current;
  const sizeValue = sizeMap[size];

  useEffect(() => {
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    );

    spinAnimation.start();

    return () => spinAnimation.stop();
  }, [spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          width: sizeValue,
          height: sizeValue,
          transform: [{ rotate: spin }],
        },
      ]}
    >
      <Svg width={sizeValue} height={sizeValue} viewBox="0 0 50 50">
        <Circle
          cx="25"
          cy="25"
          r="20"
          stroke={color}
          strokeWidth="5"
          fill="none"
          opacity={0.3}
        />
        <Circle
          cx="25"
          cy="25"
          r="20"
          stroke={color}
          strokeWidth="5"
          fill="none"
          strokeDasharray="100"
          strokeDashoffset="75"
        />
      </Svg>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
  },
});
