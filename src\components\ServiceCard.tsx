import React from "react";

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  onClick?: () => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="flex items-center gap-3 p-4 bg-white border border-[#E3DBE5] rounded-lg hover:shadow-md transition-shadow"
    >
      <div className="flex-shrink-0">{icon}</div>
      <div className="text-left">
        <h3 className="text-[#171217] text-[13px] font-bold font-['Lexend'] leading-5">
          {title}
        </h3>
      </div>
    </button>
  );
};

export default ServiceCard;
