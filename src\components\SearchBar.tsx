import React from "react";
import { SearchIcon } from "./Icons";

const SearchBar: React.FC = () => {
  return (
    <div className="px-4 py-3">
      <div className="flex items-center bg-[#F2F0F5] rounded-xl min-h-[48px]">
        <div className="flex items-center justify-center pl-4">
          <SearchIcon className="text-[#80618A]" size={20} />
        </div>
        <input
          type="text"
          placeholder="How can we help you?"
          className="flex-1 bg-transparent px-2 py-2 text-[#80618A] placeholder-[#80618A] text-sm font-['Lexend'] outline-none"
        />
      </div>
    </div>
  );
};

export default SearchBar;
