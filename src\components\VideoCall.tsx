import React, { useState, useEffect } from "react";

interface VideoCallProps {
  onEndCall?: () => void;
  onGoBack?: () => void;
}

const VideoCall: React.FC<VideoCallProps> = ({ onEndCall, onGoBack }) => {
  const [minutes, setMinutes] = useState(23);
  const [seconds, setSeconds] = useState(45);
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(true);

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setSeconds((prev) => {
        if (prev === 59) {
          setMinutes((m) => m + 1);
          return 0;
        }
        return prev + 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const BackArrowIcon = () => (
    <svg width="18" height="16" viewBox="0 0 18 16" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 8C18 8.41421 17.6642 8.75 17.25 8.75H2.56031L8.03063 14.2194C8.32368 14.5124 8.32368 14.9876 8.03063 15.2806C7.73757 15.5737 7.26243 15.5737 6.96937 15.2806L0.219375 8.53063C0.0785422 8.38995 -0.000590086 8.19906 -0.000590086 8C-0.000590086 7.80094 0.0785422 7.61005 0.219375 7.46937L6.96937 0.719375C7.26243 0.426319 7.73757 0.426319 8.03063 0.719375C8.32368 1.01243 8.32368 1.48757 8.03063 1.78062L2.56031 7.25H17.25C17.6642 7.25 18 7.58579 18 8Z"
        fill="white"
      />
    </svg>
  );

  const PlayIcon = () => (
    <svg width="17" height="20" viewBox="0 0 17 20" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.5 10C16.5013 10.518 16.2308 10.9987 15.7875 11.2666L2.28 19.5297C1.81627 19.8137 1.23518 19.8244 0.76125 19.5578C0.291875 19.2954 0.000784397 18.7999 0 18.2622V1.73781C0.000784397 1.20005 0.291875 0.704625 0.76125 0.442188C1.23518 0.175588 1.81627 0.186349 2.28 0.470313L15.7875 8.73344C16.2308 9.00131 16.5013 9.48202 16.5 10Z"
        fill="white"
      />
    </svg>
  );

  const MicIcon = () => (
    <svg width="14" height="18" viewBox="0 0 14 18" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 12.75C9.07018 12.7478 10.7478 11.0702 10.75 9V4C10.75 1.92893 9.07107 0.25 7 0.25C4.92893 0.25 3.25 1.92893 3.25 4V9C3.25215 11.0702 4.92982 12.7478 7 12.75ZM4.5 4C4.5 2.61929 5.61929 1.5 7 1.5C8.38071 1.5 9.5 2.61929 9.5 4V9C9.5 10.3807 8.38071 11.5 7 11.5C5.61929 11.5 4.5 10.3807 4.5 9V4ZM7.625 15.2188V17.125C7.625 17.4702 7.34518 17.75 7 17.75C6.65482 17.75 6.375 17.4702 6.375 17.125V15.2188C3.18323 14.894 0.753942 12.2082 0.75 9C0.75 8.65482 1.02982 8.375 1.375 8.375C1.72018 8.375 2 8.65482 2 9C2 11.7614 4.23858 14 7 14C9.76142 14 12 11.7614 12 9C12 8.65482 12.2798 8.375 12.625 8.375C12.9702 8.375 13.25 8.65482 13.25 9C13.2461 12.2082 10.8168 14.894 7.625 15.2188Z"
        fill="white"
      />
    </svg>
  );

  const CameraIcon = () => (
    <svg width="18" height="14" viewBox="0 0 18 14" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.8469 5.22969L8.09688 2.72969C7.90504 2.6017 7.65832 2.58975 7.45501 2.69859C7.2517 2.80743 7.12485 3.01939 7.125 3.25V8.25C7.12485 8.48061 7.2517 8.69256 7.45501 8.80141C7.65832 8.91025 7.90504 8.8983 8.09688 8.77031L11.8469 6.27031C12.021 6.15444 12.1256 5.95915 12.1256 5.75C12.1256 5.54085 12.021 5.34556 11.8469 5.22969ZM8.375 7.08203V4.42188L10.3734 5.75L8.375 7.08203ZM15.875 0.125H2.125C1.43464 0.125 0.875 0.684644 0.875 1.375V10.125C0.875 10.8154 1.43464 11.375 2.125 11.375H15.875C16.5654 11.375 17.125 10.8154 17.125 10.125V1.375C17.125 0.684644 16.5654 0.125 15.875 0.125ZM15.875 10.125H2.125V1.375H15.875V10.125ZM17.125 13.25C17.125 13.5952 16.8452 13.875 16.5 13.875H1.5C1.15482 13.875 0.875 13.5952 0.875 13.25C0.875 12.9048 1.15482 12.625 1.5 12.625H16.5C16.8452 12.625 17.125 12.9048 17.125 13.25Z"
        fill="white"
      />
    </svg>
  );

  const PhoneIcon = () => (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.3727 11.3797L11.6922 9.73047L11.682 9.72578C11.2939 9.55976 10.8481 9.60089 10.4969 9.83516C10.4765 9.8486 10.457 9.86321 10.4383 9.87891L8.53672 11.5C7.33203 10.9148 6.08828 9.68047 5.50313 8.49141L7.12656 6.56094C7.14219 6.54141 7.15703 6.52187 7.17109 6.50078C7.40027 6.15059 7.43898 5.70894 7.27422 5.32422V5.31484L5.62031 1.62812C5.40008 1.11992 4.87167 0.817244 4.32188 0.884375C2.13195 1.17255 0.496041 3.04119 0.5 5.25C0.5 11.4531 5.54688 16.5 11.75 16.5C13.9588 16.504 15.8275 14.8681 16.1156 12.6781C16.1829 12.1285 15.8806 11.6002 15.3727 11.3797ZM11.75 15.25C6.22965 15.244 1.75603 10.7703 1.75 5.25C1.74392 3.67182 2.90996 2.33424 4.47422 2.125C4.47391 2.12812 4.47391 2.13126 4.47422 2.13438L6.11484 5.80625L4.5 7.73906C4.48361 7.75792 4.46872 7.77804 4.45547 7.79922C4.21634 8.16615 4.18671 8.63147 4.37734 9.02578C5.08516 10.4734 6.54375 11.9211 8.00703 12.6281C8.40429 12.817 8.87158 12.7832 9.2375 12.5391C9.25756 12.5256 9.27686 12.5109 9.29531 12.4953L11.1945 10.875L14.8664 12.5195C14.8664 12.5195 14.8727 12.5195 14.875 12.5195C14.6683 14.0861 13.3301 15.2553 11.75 15.25Z"
        fill="white"
      />
    </svg>
  );

  const PlusIcon = () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 9C18 9.41421 17.6642 9.75 17.25 9.75H9.75V17.25C9.75 17.6642 9.41421 18 9 18C8.58579 18 8.25 17.6642 8.25 17.25V9.75H0.75C0.335786 9.75 0 9.41421 0 9C0 8.58579 0.335786 8.25 0.75 8.25H8.25V0.75C8.25 0.335786 8.58579 0 9 0C9.41421 0 9.75 0.335786 9.75 0.75V8.25H17.25C17.6642 8.25 18 8.58579 18 9Z"
        fill="white"
      />
    </svg>
  );

  return (
    <div className="w-full max-w-[390px] mx-auto h-screen bg-[#0F1A24] flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-4 bg-[#0F1A24]">
        <button
          onClick={onGoBack}
          className="w-12 h-12 flex items-center justify-center"
        >
          <BackArrowIcon />
        </button>
        <div className="flex-1 text-center pr-12">
          <h1 className="text-white text-lg font-bold font-['Manrope']">
            Dr. Amelia Chen
          </h1>
        </div>
      </div>

      {/* Video Section */}
      <div className="h-[219px] flex justify-center items-center relative">
        <div
          className="w-full h-full flex items-center justify-center relative"
          style={{
            background: "linear-gradient(135deg, #0D80F2 0%, #4A9FFF 100%)",
          }}
        >
          {/* Laptop illustration with doctor */}
          <div className="relative">
            <div className="w-72 h-44 bg-gray-700 rounded-lg relative overflow-hidden">
              {/* Screen content */}
              <div className="w-full h-full bg-gradient-to-br from-blue-200 to-blue-300 flex items-center justify-center">
                <div className="flex items-center space-x-4">
                  {/* Patient avatar */}
                  <div className="w-16 h-16 bg-orange-400 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-orange-500 rounded-full"></div>
                  </div>
                  {/* Doctor avatar */}
                  <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center">
                    <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center text-white text-xs">
                      👨‍⚕️
                    </div>
                  </div>
                </div>
              </div>
              {/* Video controls overlay */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-600 rounded-full"></div>
              </div>
            </div>
            {/* Laptop base */}
            <div className="w-80 h-4 bg-gray-600 rounded-b-xl mx-auto relative">
              <div className="absolute inset-x-0 bottom-0 h-2 bg-gray-500 rounded-b-xl"></div>
            </div>
            {/* Plant decoration */}
            <div className="absolute -left-12 bottom-0">
              <div className="w-8 h-12 bg-red-400 rounded-sm relative">
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="w-3 h-6 bg-green-400 rounded-full transform rotate-12"></div>
                  <div className="w-3 h-6 bg-green-500 rounded-full transform -rotate-12 absolute top-0"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Play button overlay */}
          <button className="absolute w-16 h-16 bg-black/40 rounded-full flex items-center justify-center">
            <PlayIcon />
          </button>
        </div>
      </div>

      {/* Recording Status */}
      <div className="px-4 py-1 text-center">
        <p className="text-[#8FADCC] text-sm font-['Manrope']">
          Recording in progress
        </p>
      </div>

      {/* Timer */}
      <div className="px-4 py-6 flex gap-4">
        <div className="flex flex-col items-center gap-4">
          <div className="w-[171px] h-14 bg-[#21364A] rounded-lg flex items-center justify-center">
            <span className="text-white text-lg font-bold font-['Manrope']">
              {minutes.toString().padStart(2, "0")}
            </span>
          </div>
          <span className="text-white text-sm font-['Manrope']">Minutes</span>
        </div>
        <div className="flex flex-col items-center gap-4">
          <div className="w-[171px] h-14 bg-[#21364A] rounded-lg flex items-center justify-center">
            <span className="text-white text-lg font-bold font-['Manrope']">
              {seconds.toString().padStart(2, "0")}
            </span>
          </div>
          <span className="text-white text-sm font-['Manrope']">Seconds</span>
        </div>
      </div>

      {/* Real-time Transcription */}
      <div className="px-4 py-2">
        <h3 className="text-white text-lg font-bold font-['Manrope'] mb-1">
          Real-time Transcription
        </h3>
      </div>

      {/* Transcription Content */}
      <div className="px-4 py-1 flex-1">
        <div className="text-white text-base font-['Manrope'] leading-6">
          Dr. Chen: "Thank you for sharing your concerns, Sarah. It's important
          to address these symptoms comprehensively. Let's start with a detailed
          overview of your medical history and current medications."
        </div>
      </div>

      {/* Bottom Controls */}
      <div className="relative">
        {/* Control Buttons */}
        <div className="px-4 py-4 flex justify-center gap-2">
          {/* Mute Button */}
          <button
            onClick={() => setIsMuted(!isMuted)}
            className="flex flex-col items-center gap-2"
          >
            <div
              className={`w-10 h-10 rounded-full ${isMuted ? "bg-red-600" : "bg-[#21364A]"} flex items-center justify-center`}
            >
              <MicIcon />
            </div>
            <span className="text-white text-sm font-['Manrope']">Mute</span>
          </button>

          {/* Camera Button */}
          <button
            onClick={() => setIsCameraOn(!isCameraOn)}
            className="flex flex-col items-center gap-2 mx-8"
          >
            <div
              className={`w-10 h-10 rounded-full ${!isCameraOn ? "bg-red-600" : "bg-[#21364A]"} flex items-center justify-center`}
            >
              <CameraIcon />
            </div>
            <span className="text-white text-sm font-['Manrope']">Camera</span>
          </button>

          {/* End Call Button */}
          <button
            onClick={onEndCall}
            className="flex flex-col items-center gap-2"
          >
            <div className="w-10 h-10 rounded-full bg-red-600 flex items-center justify-center">
              <PhoneIcon />
            </div>
            <span className="text-white text-sm font-['Manrope']">
              End Call
            </span>
          </button>
        </div>

        {/* Plus Button */}
        <div className="absolute bottom-5 right-5">
          <button className="w-14 h-14 bg-[#0D80F2] rounded-lg flex items-center justify-center">
            <PlusIcon />
          </button>
        </div>
      </div>

      {/* Bottom Spacer */}
      <div className="h-5 bg-[#0F1A24]"></div>
    </div>
  );
};

export default VideoCall;
