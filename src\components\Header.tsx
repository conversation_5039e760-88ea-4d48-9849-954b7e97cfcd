import React from "react";
import { SettingsIcon } from "./Icons";

const Header: React.FC = () => {
  return (
    <div className="flex justify-between items-center px-4 py-4 bg-white">
      <div className="flex-1 flex justify-center">
        <h1 className="text-lg font-bold text-[#171217] font-['Lexend']">
          AZMed+
        </h1>
      </div>
      <button className="w-12 h-12 flex items-center justify-center rounded-xl">
        <SettingsIcon className="text-[#171217]" size={24} />
      </button>
    </div>
  );
};

export default Header;
