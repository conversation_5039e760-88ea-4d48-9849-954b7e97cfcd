# Fusion Starter - React Native

A modern, production-ready React Native application with TypeScript, navigation, and a comprehensive UI component library.

## Features

- **React Native 0.73** with TypeScript
- **React Navigation 6** for navigation
- **React Query** for data fetching and caching
- **Reanimated 3** for smooth animations
- **Gesture Handler** for advanced touch interactions
- **Safe Area Context** for proper device handling
- **Custom UI Component Library** with consistent design system
- **Theme Support** with light/dark mode
- **Toast Notifications** system
- **API Service** with network detection
- **Production-ready architecture**

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **yarn**
- **React Native CLI**: `npm install -g react-native-cli`

### For iOS Development

- **Xcode** (latest version)
- **iOS Simulator**
- **CocoaPods**: `sudo gem install cocoapods`

### For Android Development

- **Android Studio**
- **Android SDK**
- **Java Development Kit (JDK 11)**

## Installation

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd fusion-starter
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Install iOS dependencies** (iOS only)
   ```bash
   cd ios && pod install && cd ..
   ```

## Running the App

### Development

```bash
# Start the Metro bundler
npm start

# Run on iOS
npm run ios

# Run on Android
npm run android
```

### Production Build

```bash
# Build for Android
npm run build:android

# Build for iOS
npm run build:ios
```

## Project Structure

```
src/
├── components/
│   └── ui/           # Reusable UI components
├── hooks/            # Custom React hooks
├── lib/              # Utility functions
├── screens/          # Screen components
├── services/         # API services
├── theme/            # Theme configuration
├── types/            # TypeScript type definitions
└── App.tsx           # Main app component
```

## Available Scripts

- `npm start` - Start the Metro bundler
- `npm run ios` - Run the app on iOS simulator
- `npm run android` - Run the app on Android emulator
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run build:android` - Build for Android
- `npm run build:ios` - Build for iOS

## UI Components

The app includes a comprehensive set of UI components:

- **Button** - Various button styles and states
- **Input** - Text input with validation support
- **Card** - Container component with elevation
- **LoadingSpinner** - Animated loading indicator
- **Toast** - Notification system

### Usage Example

```tsx
import { Button, Card, Input, useToast } from "./src/components/ui";

const MyScreen = () => {
  const { showToast } = useToast();

  return (
    <Card>
      <Input
        label="Email"
        placeholder="Enter your email"
        keyboardType="email-address"
      />
      <Button
        title="Submit"
        onPress={() => showToast({ title: "Success!", variant: "success" })}
      />
    </Card>
  );
};
```

## Navigation

The app uses React Navigation 6 with Stack Navigator. To add new screens:

1. Create your screen component in `src/screens/`
2. Add the screen type to `RootStackParamList` in `src/types/index.ts`
3. Add the screen to the navigator in `src/App.tsx`

## Theme System

The app supports light and dark themes. Use the `useTheme` hook to access theme values:

```tsx
import { useTheme } from "./src/hooks/useTheme";

const MyComponent = () => {
  const { theme, isDark, toggleTheme } = useTheme();

  return (
    <View style={{ backgroundColor: theme.colors.background }}>
      <Text style={{ color: theme.colors.text }}>Hello World</Text>
    </View>
  );
};
```

## API Integration

Use the built-in API service for network requests:

```tsx
import { apiService } from "./src/services/api";

const fetchUser = async (userId: string) => {
  try {
    const response = await apiService.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user:", error);
  }
};
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
