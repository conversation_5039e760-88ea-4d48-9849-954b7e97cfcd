import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import { Button } from "../Button";

describe("Button", () => {
  it("renders correctly", () => {
    const { getByText } = render(
      <Button title="Test Button" onPress={() => {}} />,
    );
    expect(getByText("Test Button")).toBeTruthy();
  });

  it("calls onPress when pressed", () => {
    const mockPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockPress} />,
    );

    fireEvent.press(getByText("Test Button"));
    expect(mockPress).toHaveBeenCalledTimes(1);
  });

  it("shows loading state", () => {
    const { getByTestId } = render(
      <Button title="Test Button" onPress={() => {}} loading />,
    );
    expect(getByTestId("activity-indicator")).toBeTruthy();
  });

  it("is disabled when disabled prop is true", () => {
    const mockPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockPress} disabled />,
    );

    fireEvent.press(getByText("Test Button"));
    expect(mockPress).not.toHaveBeenCalled();
  });
});
