import React from "react";
import { useNavigate } from "react-router-dom";
import VideoCall from "../components/VideoCall";

const VideoCallPage: React.FC = () => {
  const navigate = useNavigate();

  const handleEndCall = () => {
    // You can add call ending logic here
    navigate("/");
  };

  const handleGoBack = () => {
    navigate("/");
  };

  return <VideoCall onEndCall={handleEndCall} onGoBack={handleGoBack} />;
};

export default VideoCallPage;
