import React from "react";

interface EventCardProps {
  title: string;
  doctor: string;
  imageUrl?: string;
  onClick?: () => void;
}

const EventCard: React.FC<EventCardProps> = ({
  title,
  doctor,
  imageUrl,
  onClick,
}) => {
  return (
    <button
      onClick={onClick}
      className="flex flex-col min-w-[160px] flex-1 text-left"
    >
      <div
        className="h-[97px] rounded-xl mb-4 bg-gradient-to-br from-green-100 to-green-200"
        style={{
          backgroundImage: imageUrl ? `url(${imageUrl})` : undefined,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        {!imageUrl && (
          <div className="w-full h-full rounded-xl bg-gradient-to-br from-teal-400 to-green-500 flex items-center justify-center">
            <span className="text-white text-sm font-bold">Webinar</span>
          </div>
        )}
      </div>
      <div>
        <h3 className="text-[#171217] text-base font-['Lexend'] leading-6 mb-1">
          {title}
        </h3>
        <p className="text-[#80618A] text-sm font-['Lexend'] leading-[21px]">
          {doctor}
        </p>
      </div>
    </button>
  );
};

export default EventCard;
