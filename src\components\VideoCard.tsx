import React from "react";

interface VideoCardProps {
  title: string;
  doctor: string;
  thumbnailUrl?: string;
  onClick?: () => void;
}

const VideoCard: React.FC<VideoCardProps> = ({
  title,
  doctor,
  thumbnailUrl,
  onClick,
}) => {
  return (
    <button
      onClick={onClick}
      className="flex flex-col min-w-[160px] flex-1 text-left"
    >
      <div
        className="h-[97px] rounded-xl mb-4 bg-gradient-to-br from-blue-100 to-blue-200 relative overflow-hidden"
        style={{
          backgroundImage: thumbnailUrl ? `url(${thumbnailUrl})` : undefined,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        {!thumbnailUrl && (
          <div className="w-full h-full rounded-xl bg-gradient-to-br from-blue-400 to-teal-500 flex items-center justify-center">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <div className="w-0 h-0 border-l-[6px] border-l-white border-y-[4px] border-y-transparent ml-1"></div>
            </div>
          </div>
        )}
      </div>
      <div>
        <h3 className="text-[#171217] text-base font-['Lexend'] leading-6 mb-1">
          {title}
        </h3>
        <p className="text-[#80618A] text-sm font-['Lexend'] leading-[21px]">
          {doctor}
        </p>
      </div>
    </button>
  );
};

export default VideoCard;
